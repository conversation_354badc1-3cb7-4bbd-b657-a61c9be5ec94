/**
 * 第三方系统集成配置
 */

// 迎春花质控系统配置
export const yingchunhuaConfig = {
  // 开发环境配置
  development: {
    sdkUrl: 'http://183.242.68.188:32103/client_app_iframe/index.js',
    appKey: 'ajNLOHF3dERoMmhob1FNSi9ZTFpJUT09', // 已提供
    appSecretKey: '', // 待项目经理提供
    linkType: '2', // 1-app, 2-web
    qualityTarget: '2' // 1-临床, 2-病理
  },
  
  // 测试环境配置
  test: {
    sdkUrl: 'http://183.242.68.188:8094/client_app_iframe/index.js',
    appKey: 'ajNLOHF3dERoMmhob1FNSi9ZTFpJUT09',
    appSecretKey: '', // 待项目经理提供
    linkType: '2',
    qualityTarget: '2'
  },

  // 生产环境配置
  production: {
    sdkUrl: 'http://183.242.68.188:8094/client_app_iframe/index.js',
    appKey: 'ajNLOHF3dERoMmhob1FNSi9ZTFpJUT09',
    appSecretKey: '', // 待项目经理提供
    linkType: '2',
    qualityTarget: '2'
  }
}


// 获取当前环境配置
export const getCurrentConfig = (system = 'yingchunhua') => {
  const env = process.env.NODE_ENV || 'development'
  
  const configs = {
    yingchunhua: yingchunhuaConfig
  }
  
  return configs[system]?.[env] || configs[system]?.development
}

// 患者数据字段映射配置
export const patientDataMapping = {
  // 必填字段
  required: [
    'patient_id',
    'visit_sn', 
    'visit_type',
    'hospital_code',
    'hospital_name',
    'visit_doctor_no',
    'visit_doctor_name',
    'name',
    'gender',
    'date_of_birth',
    'occupation_code',
    'occupation_name'
  ],
  
  // 住院患者必填字段
  inpatientRequired: [
    'medical_record_no',
    'inpatient_no',
    'admission_datetime'
  ],
  
  // 门诊患者必填字段
  outpatientRequired: [
    'outpatient_no',
    'visit_datetime',
    'regis_sn',
    'regis_datetime',
    'regis_dept_code',
    'regis_dept_name'
  ],
  
  // 字段默认值
  defaults: {
    patient_gender: 'NULL',
    nationality: '中国',
    ethnicity: '汉族',
    newbron_mark: '否',
    visit_status: '否',
    patient_identity: '其他',
    blood_type_s: 'NULL',
    bolld_type_e: 'NULL',
    height: 'NULL',
    weight: 'NULL',
    certificate_type: '身份证',
    health_card_type: 'NULL',
    health_card_no: 'NULL',
    tsblbs: 'NULL',
    is_hospital_infected: 'NULL',
    extend_data1: 'NULL',
    extend_data2: 'NULL',
    record_status: '1',
    first_visit_mark: '是',
    regis_charge_price: '0.000',
    regis_paid_price: '0.000'
  }
}



// 质控目标映射
export const qualityTargetMapping = {
  '1': '临床',
  '2': '病理'
}


export default {
  getCurrentConfig,
  patientDataMapping,
}
